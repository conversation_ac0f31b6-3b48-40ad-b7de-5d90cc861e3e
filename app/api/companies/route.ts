import { NextRequest } from 'next/server'
import { db, uid } from '@/lib/db'
import { companies } from '@/lib/schema-postgres'
import { eq } from 'drizzle-orm'
import { withApiAuthRequired, getSession } from '@auth0/nextjs-auth0'
import { createSuccessResponse, createErrorResponse } from '@/lib/api-helpers'

// GET /api/companies - Get current user's company profile
export const GET = withApiAuthRequired(async function GET(request: NextRequest) {
  try {
    const session = await getSession()
    if (!session?.user) {
      return createErrorResponse('Unauthorized', 401)
    }

    const company = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub),
    })

    if (!company) {
      return createSuccessResponse({ company: null })
    }

    return createSuccessResponse({ company })
  } catch (error) {
    console.error('Error fetching company:', error)
    return createErrorResponse('Failed to fetch company profile')
  }
})

// POST /api/companies - Create or update company profile
export const POST = withApiAuthRequired(async function POST(request: NextRequest) {
  try {
    const session = await getSession()
    if (!session?.user) {
      return createErrorResponse('Unauthorized', 401)
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.name || !body.email) {
      return createErrorResponse('Company name and email are required', 400)
    }

    // Check if company already exists
    const existingCompany = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub),
    })

    const companyData = {
      auth0_user_id: session.user.sub,
      name: body.name,
      email: body.email,
      phone: body.phone || null,
      address: body.address || null,
      city: body.city || null,
      country: body.country || null,
      tax_id: body.tax_id || null,
      bank: body.bank || null,
      incoterm: body.incoterm || null,
      payment_term: body.payment_term || null,
      onboarding_completed: body.onboarding_completed === 'true' || body.onboarding_completed === true,
      status: body.status || 'active',
      updated_at: new Date(),
    }

    let company
    if (existingCompany) {
      // Update existing company
      await db.update(companies)
        .set(companyData)
        .where(eq(companies.id, existingCompany.id))
      
      company = await db.query.companies.findFirst({
        where: eq(companies.id, existingCompany.id),
      })
    } else {
      // Create new company
      const companyId = uid('company')
      await db.insert(companies).values({
        id: companyId,
        ...companyData,
      })
      
      company = await db.query.companies.findFirst({
        where: eq(companies.id, companyId),
      })
    }

    return createSuccessResponse({ 
      company,
      message: existingCompany ? 'Company profile updated successfully' : 'Company profile created successfully'
    })
  } catch (error) {
    console.error('Error creating/updating company:', error)
    return createErrorResponse('Failed to save company profile')
  }
})

// PUT /api/companies - Update company profile (alias for POST)
export const PUT = withApiAuthRequired(async function PUT(request: NextRequest) {
  return POST(request)
})
