"use client"

import { useUser } from '@auth0/nextjs-auth0/client'
import { AppShell } from "@/components/app-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Building,
  Users,
  Package,
  ShoppingCart,
  TrendingUp,
  DollarSign,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react"
import { useEffect, useState } from 'react'

interface DashboardStats {
  customers: number
  products: number
  suppliers: number
  salesContracts: number
  purchaseContracts: number
  samples: number
}

export default function DashboardPage() {
  const { user, isLoading } = useUser()
  const [stats, setStats] = useState<DashboardStats>({
    customers: 0,
    products: 0,
    suppliers: 0,
    salesContracts: 0,
    purchaseContracts: 0,
    samples: 0
  })
  const [statsLoading, setStatsLoading] = useState(true)
  const [statsError, setStatsError] = useState<string | null>(null)

  // Fetch dashboard statistics from secure API endpoints
  useEffect(() => {
    if (!user) return

    const fetchStats = async () => {
      try {
        setStatsLoading(true)
        setStatsError(null)

        // First, ensure company exists for this user
        console.log('🏢 Ensuring company exists for user:', user.email)
        const ensureCompanyRes = await fetch('/api/companies/ensure', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!ensureCompanyRes.ok) {
          throw new Error('Failed to ensure company exists')
        }

        const companyResult = await ensureCompanyRes.json()
        console.log('🏢 Company status:', companyResult)

        // Now fetch data from all secured endpoints
        const [customersRes, productsRes, suppliersRes, salesContractsRes, purchaseContractsRes, samplesRes] = await Promise.allSettled([
          fetch('/api/customers'),
          fetch('/api/products'),
          fetch('/api/suppliers'),
          fetch('/api/contracts/sales'),
          fetch('/api/contracts/purchase'),
          fetch('/api/samples')
        ])

        const newStats: DashboardStats = {
          customers: 0,
          products: 0,
          suppliers: 0,
          salesContracts: 0,
          purchaseContracts: 0,
          samples: 0
        }

        // Process customers
        if (customersRes.status === 'fulfilled' && customersRes.value.ok) {
          const customers = await customersRes.value.json()
          newStats.customers = Array.isArray(customers) ? customers.length : 0
        }

        // Process products
        if (productsRes.status === 'fulfilled' && productsRes.value.ok) {
          const products = await productsRes.value.json()
          newStats.products = Array.isArray(products) ? products.length : 0
        }

        // Process suppliers
        if (suppliersRes.status === 'fulfilled' && suppliersRes.value.ok) {
          const suppliers = await suppliersRes.value.json()
          newStats.suppliers = Array.isArray(suppliers) ? suppliers.length : 0
        }

        // Process sales contracts
        if (salesContractsRes.status === 'fulfilled' && salesContractsRes.value.ok) {
          const salesContracts = await salesContractsRes.value.json()
          newStats.salesContracts = Array.isArray(salesContracts) ? salesContracts.length : 0
        }

        // Process purchase contracts
        if (purchaseContractsRes.status === 'fulfilled' && purchaseContractsRes.value.ok) {
          const purchaseContracts = await purchaseContractsRes.value.json()
          newStats.purchaseContracts = Array.isArray(purchaseContracts) ? purchaseContracts.length : 0
        }

        // Process samples
        if (samplesRes.status === 'fulfilled' && samplesRes.value.ok) {
          const samples = await samplesRes.value.json()
          newStats.samples = Array.isArray(samples) ? samples.length : 0
        }

        setStats(newStats)
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
        setStatsError('Failed to load dashboard statistics')
      } finally {
        setStatsLoading(false)
      }
    }

    fetchStats()
  }, [user])

  if (isLoading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </AppShell>
    )
  }

  if (statsError) {
    return (
      <AppShell>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Dashboard Error</h2>
            <p className="text-muted-foreground mb-4">{statsError}</p>
            <p className="text-sm text-muted-foreground">
              This may indicate you need to complete your company profile setup.
            </p>
            <Button onClick={() => window.location.reload()} className="mt-4">
              Retry
            </Button>
          </div>
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">
            Welcome back{user?.name ? `, ${user.name}` : ''}!
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your manufacturing operations today.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.customers
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Active customer accounts
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.products
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Products in catalog
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Suppliers</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.suppliers
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Active suppliers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Contracts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.salesContracts + stats.purchaseContracts
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Sales & purchase contracts
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions & Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Common tasks to get you started
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button asChild className="w-full justify-start">
                <a href="/customers">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Customers
                </a>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <a href="/products">
                  <Package className="h-4 w-4 mr-2" />
                  View Products
                </a>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <a href="/sales-contracts">
                  <FileText className="h-4 w-4 mr-2" />
                  Create Sales Contract
                </a>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <a href="/company-profile">
                  <Building className="h-4 w-4 mr-2" />
                  Update Company Profile
                </a>
              </Button>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                System Status
              </CardTitle>
              <CardDescription>
                Your ERP system setup progress
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Company Profile</span>
                </div>
                <Badge variant="default">Complete</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Customer Database</span>
                </div>
                <Badge variant="default">Active</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Product Catalog</span>
                </div>
                <Badge variant="default">Ready</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <span className="text-sm">First Sales Contract</span>
                </div>
                <Badge variant="secondary">Pending</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                  <span className="text-sm">Inventory Setup</span>
                </div>
                <Badge variant="secondary">Pending</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Getting Started Guide */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>🚀 Getting Started</CardTitle>
            <CardDescription>
              Complete these steps to get the most out of your Manufacturing ERP system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <h3 className="font-semibold">1. Company Setup</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  Your company profile is complete and ready for business.
                </p>
                <Badge variant="default">Complete</Badge>
              </div>
              
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-5 w-5 text-orange-500" />
                  <h3 className="font-semibold">2. Create Your First Contract</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  Start generating revenue by creating your first sales contract.
                </p>
                <Button asChild size="sm">
                  <a href="/sales-contracts">Create Contract</a>
                </Button>
              </div>
              
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                  <h3 className="font-semibold">3. Set Up Inventory</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  Track your raw materials and finished goods inventory.
                </p>
                <Button asChild size="sm" variant="outline">
                  <a href="/inventory">Setup Inventory</a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
