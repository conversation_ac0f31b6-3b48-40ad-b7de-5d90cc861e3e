"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Plus, Trash2, FilePen, Eye } from "lucide-react";
import { ContractDocumentViewer } from "@/components/contract-document-viewer";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Type definitions matching the schema
type PurchaseContract = {
  id: string;
  number: string;
  supplier_id: string;
  template_id?: string;
  date: string;
  currency?: string;
  status: string;
  created_at: Date | null;
  supplier: {
    id: string;
    name: string;
    contact_name: string | null;
    contact_phone: string | null;
    contact_email: string | null;
    address: string | null;
    tax_id: string | null;
    bank: string | null;
    status: string | null;
    created_at: Date | null;
  };
  items: {
    id: string;
    contract_id: string;
    product_id: string;
    qty: string;
    price: string;
    product: {
      id: string;
      sku: string;
      name: string;
      unit: string;
      hs_code: string | null;
      origin: string | null;
      package: string | null;
      image: string | null;
      created_at: Date | null;
    };
  }[];
};

type Supplier = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  tax_id: string | null;
  bank: string | null;
  status: string | null;
  created_at: Date | null;
};

type Product = {
  id: string;
  sku: string;
  name: string;
  unit: string;
  hs_code: string | null;
  origin: string | null;
  package: string | null;
  image: string | null;
  created_at: Date | null;
};

type ContractTemplate = {
  id: string;
  name: string;
  type: string;
  content: string;
  currency?: string;
  payment_terms?: string;
  delivery_terms?: string;
  language: string;
  version: number;
  is_active: boolean;
  created_at: Date | null;
};

const formSchema = z.object({
  number: z.string().min(1, "Contract number is required."),
  supplierId: z.string().min(1, "Supplier is required."),
  template_id: z.string().optional(),
  currency: z.string().optional(),
  items: z.array(
    z.object({
      productId: z.string().min(1, "Product is required."),
      qty: z.coerce.number().min(0.01, "Quantity must be greater than 0."),
      price: z.coerce.number().min(0.01, "Price must be greater than 0."),
    }),
  ).min(1, "At least one item is required."),
});

function AddPurchaseContractForm({
  setOpen,
  suppliers,
  products,
  templates,
}: {
  setOpen: (open: boolean) => void;
  suppliers: Supplier[];
  products: Product[];
  templates: ContractTemplate[];
}) {
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      number: "",
      supplierId: "",
      template_id: "none",
      currency: "",
      items: [],
    },
  });
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      // Convert "none" template selection to undefined
      const submitData = {
        ...values,
        template_id: values.template_id === "none" ? undefined : values.template_id
      };

      console.log("Submitting purchase contract:", submitData); // Debug log
      console.log("Items structure:", submitData.items); // Debug items specifically
      const response = await fetch("/api/contracts/purchase", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        toast.success("Purchase contract created successfully.");
        setOpen(false);
        router.refresh();
      } else {
        const errorData = await response.json();
        console.error("API Error:", errorData); // Debug log
        console.error("Validation Details:", errorData.details); // Show validation details
        toast.error(`Failed to create purchase contract: ${errorData.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error("Submit error:", error);
      toast.error("Failed to create purchase contract.");
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contract Number</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="supplierId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Supplier</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a supplier" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {suppliers.map((s) => (
                      <SelectItem key={s.id} value={s.id}>
                        {s.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="template_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contract Template (Optional)</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a template" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">No template</SelectItem>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="e.g. USD" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-2">
          <FormLabel>Items</FormLabel>
          <div className="space-y-4 rounded-md border p-4">
            {fields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-4 gap-4 items-end">
                <FormField
                  control={form.control}
                  name={`items.${index}.productId`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select product" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {products.map((p) => (
                            <SelectItem key={p.id} value={p.id}>
                              {p.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`items.${index}.qty`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantity</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`items.${index}.price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <Button type="button" variant="destructive" size="sm" onClick={() => remove(index)}>
                  Remove
                </Button>
              </div>
            ))}
            <Button type="button" variant="outline" size="sm" onClick={() => append({ productId: "", qty: 1, price: 0 })}>
              Add Item
            </Button>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="ghost" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button type="submit">Create Contract</Button>
        </div>
      </form>
    </Form>
  );
}

function EditPurchaseContractForm({
  contract,
  setOpen,
  suppliers,
  products,
}: {
  contract: PurchaseContract;
  setOpen: (open: boolean) => void;
  suppliers: Supplier[];
  products: Product[];
}) {
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      number: contract.number,
      supplierId: contract.supplier_id,
      currency: contract.currency || "",
      items: contract.items.map(item => ({
        productId: item.product_id,
        qty: parseFloat(item.qty),
        price: parseFloat(item.price),
      })),
    },
  });
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      const response = await fetch(`/api/contracts/purchase/${contract.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        toast.success("Purchase contract updated successfully.");
        setOpen(false);
        router.refresh();
      } else {
        const errorData = await response.json();
        console.error("API Error:", errorData);
        toast.error(`Failed to update purchase contract: ${errorData.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Failed to update purchase contract.");
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contract Number</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="supplierId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Supplier</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a supplier" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {suppliers.map((s) => (
                      <SelectItem key={s.id} value={s.id}>
                        {s.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="e.g. USD" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-2">
          <FormLabel>Items</FormLabel>
          <div className="space-y-4 rounded-md border p-4">
            {fields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-4 gap-4 items-end">
                <FormField
                  control={form.control}
                  name={`items.${index}.productId`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select product" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {products.map((p) => (
                            <SelectItem key={p.id} value={p.id}>
                              {p.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`items.${index}.qty`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantity</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`items.${index}.price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <Button type="button" variant="destructive" size="sm" onClick={() => remove(index)}>
                  Remove
                </Button>
              </div>
            ))}
            <Button type="button" variant="outline" size="sm" onClick={() => append({ productId: "", qty: 1, price: 0 })}>
              Add Item
            </Button>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button type="submit">Update Contract</Button>
        </div>
      </form>
    </Form>
  );
}

export function PurchaseContractsClientPage({
  initialContracts,
  suppliers,
  products,
}: {
  initialContracts: PurchaseContract[];
  suppliers: Supplier[];
  products: Product[];
}) {
  const router = useRouter();
  const [isAddOpen, setAddOpen] = useState(false);
  const [isEditOpen, setEditOpen] = useState(false);
  const [isViewDocOpen, setViewDocOpen] = useState(false);
  const [contractToEdit, setContractToEdit] = useState<PurchaseContract | null>(null);
  const [contractToDelete, setContractToDelete] = useState<PurchaseContract | null>(null);
  const [contractToView, setContractToView] = useState<PurchaseContract | null>(null);
  const [templates, setTemplates] = useState<ContractTemplate[]>([]);
  const [documentContent, setDocumentContent] = useState<string>("");

  // Load templates on component mount
  useEffect(() => {
    async function loadTemplates() {
      try {
        const response = await fetch("/api/contracts/templates?type=purchase");
        if (response.ok) {
          const data = await response.json();
          setTemplates(data.data?.templates || []);
        }
      } catch (error) {
        console.error("Failed to load templates:", error);
      }
    }
    loadTemplates();
  }, []);

  const handleViewDocument = async (contract: PurchaseContract) => {
    if (!contract.template_id) {
      toast.error("This contract was not created from a template.");
      return;
    }

    try {
      const requestData = {
        number: contract.number,
        supplier_id: contract.supplier_id,
        date: contract.date,
        status: contract.status,
        currency: contract.currency,
        items: contract.items.map(item => ({
          product_id: item.product_id,
          qty: item.qty,
          price: item.price,
          product: item.product
        }))
      };

      console.log('Sending purchase preview request:', requestData);

      const response = await fetch(`/api/contracts/templates/${contract.template_id}/preview`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Purchase preview response:', data);

        if (data.data && data.data.template && data.data.template.processed_content) {
          setDocumentContent(data.data.template.processed_content);
        } else if (data.template && data.template.processed_content) {
          setDocumentContent(data.template.processed_content);
        } else {
          console.error('Unexpected response structure:', data);
          toast.error("Invalid response format from server.");
          return;
        }

        setContractToView(contract);
        setViewDocOpen(true);
      } else {
        const errorText = await response.text();
        console.error('Purchase preview API error:', errorText);
        toast.error("Failed to generate contract document.");
      }
    } catch (error) {
      console.error("Error generating purchase document:", error);
      toast.error("Failed to generate contract document.");
    }
  };

  const handleDelete = async () => {
    if (!contractToDelete) return;
    const response = await fetch(`/api/contracts/purchase/${contractToDelete.id}`, { method: "DELETE" });
    if (response.ok) {
      toast.success(`Contract "${contractToDelete.number}" deleted successfully.`);
      setContractToDelete(null);
      router.refresh();
    } else {
      toast.error("Failed to delete contract.");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Purchase Contracts</h1>
          <p className="text-muted-foreground">Manage your company's purchase contracts.</p>
        </div>
        <Dialog open={isAddOpen} onOpenChange={setAddOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Contract
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Add New Purchase Contract</DialogTitle>
              <DialogDescription>Enter the details of the new purchase contract.</DialogDescription>
            </DialogHeader>
            <AddPurchaseContractForm setOpen={setAddOpen} suppliers={suppliers} products={products} templates={templates} />
          </DialogContent>
        </Dialog>

        {/* Edit Dialog */}
        <Dialog open={isEditOpen} onOpenChange={setEditOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Edit Purchase Contract</DialogTitle>
              <DialogDescription>Update the details of the purchase contract.</DialogDescription>
            </DialogHeader>
            {contractToEdit && (
              <EditPurchaseContractForm
                contract={contractToEdit}
                setOpen={setEditOpen}
                suppliers={suppliers}
                products={products}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Contract List</CardTitle>
          <CardDescription>A list of all purchase contracts in your system.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {initialContracts.map((contract) => (
              <Card key={contract.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-6 w-6" />
                        <CardTitle className="text-lg">{contract.number}</CardTitle>
                        <Badge>{contract.status}</Badge>
                      </div>
                      <CardDescription>Supplier: {contract.supplier.name}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      {contract.template_id && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewDocument(contract)}
                          title="View Contract Document"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setContractToEdit(contract);
                          setEditOpen(true);
                        }}
                      >
                        <FilePen className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => setContractToDelete(contract)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <h4 className="font-semibold">Items ({contract.items.length})</h4>
                  <ul className="list-disc pl-5 mt-2 text-sm">
                    {contract.items.map((item) => (
                      <li key={item.id}>
                        {item.product.name} - {item.qty} {item.product.unit} @ {item.price} {contract.currency}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Professional Contract Document Viewer */}
      <ContractDocumentViewer
        isOpen={isViewDocOpen}
        onClose={() => setViewDocOpen(false)}
        contractNumber={contractToView?.number || ""}
        documentContent={documentContent}
        contractType="purchase"
      />

      {/* Delete Alert Dialog */}
      <AlertDialog open={!!contractToDelete} onOpenChange={(open) => !open && setContractToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the contract "{contractToDelete?.number}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
