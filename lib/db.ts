import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./schema-postgres";

/**
 * Manufacturing ERP - PostgreSQL Database Connection
 *
 * Enterprise-grade PostgreSQL connection with optimized configuration
 * for multi-tenant Manufacturing ERP system
 */

// PostgreSQL connection URL with fallback options
const connectionUrl = process.env.DATABASE_URL_POSTGRESQL ||
                     process.env.POSTGRES_LOCAL_URL ||
                     process.env.DATABASE_URL ||
                     'postgresql://erp_user:SecureERP2024!@localhost:5432/manufacturing_erp';

if (!connectionUrl) {
  throw new Error("PostgreSQL DATABASE_URL is missing from environment variables");
}

// Enhanced logging for production debugging
if (process.env.NODE_ENV === 'production') {
  console.log('🔍 Database Connection Debug Info:');
  console.log('DATABASE_URL_POSTGRESQL:', process.env.DATABASE_URL_POSTGRESQL ? 'SET (length: ' + process.env.DATABASE_URL_POSTGRESQL.length + ')' : 'MISSING');
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'SET (length: ' + process.env.DATABASE_URL.length + ')' : 'MISSING');
  console.log('Selected connectionUrl hostname:', connectionUrl.split('@')[1]?.split('/')[0] || 'UNKNOWN');
  console.log('USE_POSTGRESQL:', process.env.USE_POSTGRESQL);
  console.log('NODE_ENV:', process.env.NODE_ENV);
}

// PostgreSQL client configuration optimized for Manufacturing ERP
const client = postgres(connectionUrl, {
  max: 20, // Connection pool size
  idle_timeout: 20, // Close idle connections after 20 seconds
  connect_timeout: 10, // Connection timeout in seconds
  debug: process.env.NODE_ENV === 'development', // Enable debug in development
});

// Create Drizzle instance with PostgreSQL schema
export const db = drizzle(client, { schema });

export function uid(prefix = "id") {
  const g: any = globalThis as any;
  const id =
    g.crypto?.randomUUID?.() ??
    `${Math.random().toString(36).slice(2, 10)}${Math.random().toString(36).slice(2, 10)}`;
  return `${prefix}_${id}`;
}

export function nowISO() {
  return new Date().toISOString();
}
