import { relations } from "drizzle-orm"
import { text, timestamp, pgTable, index, varchar, serial } from "drizzle-orm/pg-core"

// BASIC ENTITIES
export const customers = pgTable("customers", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  contact_name: text("contact_name"),
  contact_phone: text("contact_phone"),
  contact_email: text("contact_email"),
  address: text("address"),
  tax_id: text("tax_id"),
  bank: text("bank"),
  incoterm: text("incoterm"),
  payment_term: text("payment_term"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("customers_company_id_idx").on(table.company_id),
}))

export const suppliers = pgTable("suppliers", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  contact_name: text("contact_name"),
  contact_phone: text("contact_phone"),
  contact_email: text("contact_email"),
  address: text("address"),
  tax_id: text("tax_id"),
  bank: text("bank"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("suppliers_company_id_idx").on(table.company_id),
}))

// COMPANY PROFILE - Auth0 Integration
export const companies = pgTable("companies", {
  id: text("id").primaryKey(),
  auth0_user_id: text("auth0_user_id").notNull().unique(), // Auth0 user ID (sub claim)

  // Basic Company Information
  name: text("name").notNull(),
  legal_name: text("legal_name"), // Full legal company name
  registration_number: text("registration_number"), // Business registration number
  tax_id: text("tax_id"), // Tax identification number
  vat_number: text("vat_number"), // VAT registration number

  // Contact Information
  email: text("email"),
  phone: text("phone"),
  website: text("website"),

  // Address Information
  address_line1: text("address_line1"),
  address_line2: text("address_line2"),
  city: text("city"),
  state_province: text("state_province"),
  postal_code: text("postal_code"),
  country: text("country"),

  // Business Information
  industry: text("industry"),
  business_type: text("business_type"), // "manufacturer", "trader", "service", etc.
  employee_count: text("employee_count"),
  annual_revenue: text("annual_revenue"),

  // Banking Information
  bank_name: text("bank_name"),
  bank_account: text("bank_account"),
  bank_swift: text("bank_swift"),
  bank_address: text("bank_address"),
  bank_iban: text("bank_iban"),

  // Settings and Preferences
  currency: text("currency").default("USD"),
  timezone: text("timezone").default("UTC"),
  date_format: text("date_format").default("YYYY-MM-DD"),
  language: text("language").default("en"),

  // Trade Information
  export_license: text("export_license"),
  customs_code: text("customs_code"),
  preferred_incoterms: text("preferred_incoterms").default("FOB"),
  preferred_payment_terms: text("preferred_payment_terms").default("30 days"),

  // System Fields
  status: text("status").default("active"), // "active", "suspended", "pending"
  onboarding_completed: text("onboarding_completed").default("false"),
  onboarding_step: text("onboarding_step").default("basic_info"),
  subscription_plan: text("subscription_plan").default("free"), // "free", "basic", "premium", "enterprise"
  subscription_status: text("subscription_status").default("active"),
  trial_ends_at: timestamp("trial_ends_at", { withTimezone: true }),
  
  // Audit Fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  last_login_at: timestamp("last_login_at", { withTimezone: true }),
}, (table) => ({
  auth0UserIdIdx: index("companies_auth0_user_id_idx").on(table.auth0_user_id),
  nameIdx: index("companies_name_idx").on(table.name),
}))

export const products = pgTable("products", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  sku: text("sku").notNull(),
  name: text("name").notNull(),
  unit: text("unit").notNull(),
  hs_code: text("hs_code"),
  origin: text("origin"),
  package: text("package"),
  image: text("image"),
  category: text("category"),
  description: text("description"),
  price: text("price"),
  inspection_required: text("inspection_required").default("false"),
  quality_tolerance: text("quality_tolerance"),
  quality_notes: text("quality_notes"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("products_company_id_idx").on(table.company_id),
  skuCompanyIdx: index("products_sku_company_idx").on(table.sku, table.company_id), // Ensure SKU uniqueness per company
}))

export const samples = pgTable("samples", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  code: text("code").notNull(),
  name: text("name").notNull(),
  date: text("date").notNull(),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("samples_company_id_idx").on(table.company_id),
}))

export const salesContracts = pgTable("sales_contracts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  customer_id: text("customer_id").notNull().references(() => customers.id),
  template_id: text("template_id").references(() => contractTemplates.id),
  date: text("date").notNull(),
  currency: text("currency"),
  status: text("status").default("draft"),
  content: text("content"), // Rich text contract content
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("sales_contracts_company_id_idx").on(table.company_id),
}))

export const salesContractItems = pgTable("sales_contract_items", {
  id: text("id").primaryKey(),
  contract_id: text("contract_id").notNull().references(() => salesContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  price: text("price").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

export const purchaseContracts = pgTable("purchase_contracts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  template_id: text("template_id").references(() => contractTemplates.id),
  date: text("date").notNull(),
  currency: text("currency"),
  status: text("status").default("draft"),
  content: text("content"), // Rich text contract content
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("purchase_contracts_company_id_idx").on(table.company_id),
}))

export const purchaseContractItems = pgTable("purchase_contract_items", {
  id: text("id").primaryKey(),
  contract_id: text("contract_id").notNull().references(() => purchaseContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  price: text("price").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

// Contract Templates Schema
export const contractTemplates = pgTable("contract_templates", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  type: text("type").notNull(), // 'sales' | 'purchase'
  content: text("content").notNull(), // Rich text template content

  // Template metadata
  currency: text("currency"),
  payment_terms: text("payment_terms"),
  delivery_terms: text("delivery_terms"),
  language: text("language").default("en"),
  version: text("version").default("1"),
  description: text("description"),
  is_default: text("is_default").default("false"), // "true" | "false"
  is_active: text("is_active").default("true"),
  status: text("status").default("active"), // "active" | "inactive" | "archived"

  // Audit fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("contract_templates_company_id_idx").on(table.company_id),
}))

export const workOrders = pgTable("work_orders", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  sales_contract_id: text("sales_contract_id").references(() => salesContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  due_date: text("due_date"),
  status: text("status").default("pending"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("work_orders_company_id_idx").on(table.company_id),
}))

export const stockLots = pgTable("stock_lots", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  location: text("location").notNull(),
  lot_number: text("lot_number"),
  expiry_date: text("expiry_date"),
  status: text("status").default("available"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("stock_lots_company_id_idx").on(table.company_id),
}))

export const stockTxns = pgTable("stock_txns", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  type: text("type").notNull(), // "inbound" or "outbound"
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  reference: text("reference"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("stock_txns_company_id_idx").on(table.company_id),
}))

export const declarations = pgTable("declarations", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  status: text("status").default("draft"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("declarations_company_id_idx").on(table.company_id),
}))

export const declarationItems = pgTable("declaration_items", {
  id: text("id").primaryKey(),
  declaration_id: text("declaration_id").notNull().references(() => declarations.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  // Quality Integration Fields
  quality_inspection_id: text("quality_inspection_id").references(() => qualityInspections.id),
  quality_status: text("quality_status").default("pending"), // "pending", "approved", "rejected"
  quality_notes: text("quality_notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

export const arInvoices = pgTable("ar_invoices", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  customer_id: text("customer_id").notNull().references(() => customers.id),
  date: text("date").notNull(),
  amount: text("amount").notNull(),
  status: text("status").default("draft"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("ar_invoices_company_id_idx").on(table.company_id),
}))

export const apInvoices = pgTable("ap_invoices", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  date: text("date").notNull(),
  amount: text("amount").notNull(),
  status: text("status").default("draft"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("ap_invoices_company_id_idx").on(table.company_id),
}))

// QUALITY CONTROL TABLES
export const qualityInspections = pgTable("quality_inspections", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  work_order_id: text("work_order_id").references(() => workOrders.id),
  inspection_type: text("inspection_type").notNull(), // "incoming", "in-process", "final", "pre-shipment"
  inspector: text("inspector").notNull(),
  inspection_date: text("inspection_date").notNull(),
  status: text("status").default("pending"), // "pending", "in-progress", "completed", "failed"
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_inspections_company_id_idx").on(table.company_id),
}))

export const qualityDefects = pgTable("quality_defects", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  work_order_id: text("work_order_id").references(() => workOrders.id),
  product_id: text("product_id").references(() => products.id),
  defect_type: text("defect_type").notNull(),
  severity: text("severity").notNull(), // "minor", "major", "critical"
  description: text("description").notNull(),
  quantity_affected: text("quantity_affected"),
  corrective_action: text("corrective_action"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_defects_company_id_idx").on(table.company_id),
}))

export const qualityStandards = pgTable("quality_standards", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").references(() => products.id),
  standard_name: text("standard_name").notNull(),
  specification: text("specification").notNull(),
  tolerance: text("tolerance"),
  test_method: text("test_method"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_standards_company_id_idx").on(table.company_id),
}))

export const qualityCertificates = pgTable("quality_certificates", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  certificate_number: text("certificate_number").notNull(),
  certificate_type: text("certificate_type").notNull(), // "COA", "COC", "test_report", "compliance"
  issued_date: text("issued_date").notNull(),
  valid_until: text("valid_until"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_certificates_company_id_idx").on(table.company_id),
}))

export const inspectionResults = pgTable("inspection_results", {
  id: text("id").primaryKey(),
  inspection_id: text("inspection_id").notNull().references(() => qualityInspections.id),
  standard_id: text("standard_id").references(() => qualityStandards.id),
  measured_value: text("measured_value"),
  result: text("result").notNull(), // "pass", "fail", "conditional"
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

// RELATIONS (same as SQLite schema)
export const companiesRelations = relations(companies, ({ many }) => ({
  customers: many(customers),
  suppliers: many(suppliers),
  products: many(products),
  samples: many(samples),
  salesContracts: many(salesContracts),
  purchaseContracts: many(purchaseContracts),
  contractTemplates: many(contractTemplates),
  workOrders: many(workOrders),
  stockLots: many(stockLots),
  stockTxns: many(stockTxns),
  declarations: many(declarations),
  arInvoices: many(arInvoices),
  apInvoices: many(apInvoices),
  qualityInspections: many(qualityInspections),
  qualityDefects: many(qualityDefects),
  qualityStandards: many(qualityStandards),
  qualityCertificates: many(qualityCertificates),
}))

export const customersRelations = relations(customers, ({ one, many }) => ({
  company: one(companies, {
    fields: [customers.company_id],
    references: [companies.id],
  }),
  salesContracts: many(salesContracts),
  arInvoices: many(arInvoices),
}))

export const suppliersRelations = relations(suppliers, ({ one, many }) => ({
  company: one(companies, {
    fields: [suppliers.company_id],
    references: [companies.id],
  }),
  purchaseContracts: many(purchaseContracts),
  apInvoices: many(apInvoices),
}))

export const productsRelations = relations(products, ({ one, many }) => ({
  company: one(companies, {
    fields: [products.company_id],
    references: [companies.id],
  }),
  salesContractItems: many(salesContractItems),
  purchaseContractItems: many(purchaseContractItems),
  workOrders: many(workOrders),
  stockLots: many(stockLots),
  stockTxns: many(stockTxns),
  declarationItems: many(declarationItems),
  qualityDefects: many(qualityDefects),
  qualityStandards: many(qualityStandards),
}))

export const samplesRelations = relations(samples, ({ one }) => ({
  company: one(companies, {
    fields: [samples.company_id],
    references: [companies.id],
  }),
}))

export const salesContractsRelations = relations(salesContracts, ({ one, many }) => ({
  company: one(companies, {
    fields: [salesContracts.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [salesContracts.customer_id],
    references: [customers.id],
  }),
  template: one(contractTemplates, {
    fields: [salesContracts.template_id],
    references: [contractTemplates.id],
  }),
  items: many(salesContractItems),
  workOrders: many(workOrders),
}))

export const salesContractItemsRelations = relations(salesContractItems, ({ one }) => ({
  contract: one(salesContracts, {
    fields: [salesContractItems.contract_id],
    references: [salesContracts.id],
  }),
  product: one(products, {
    fields: [salesContractItems.product_id],
    references: [products.id],
  }),
}))

export const purchaseContractsRelations = relations(purchaseContracts, ({ one, many }) => ({
  company: one(companies, {
    fields: [purchaseContracts.company_id],
    references: [companies.id],
  }),
  supplier: one(suppliers, {
    fields: [purchaseContracts.supplier_id],
    references: [suppliers.id],
  }),
  template: one(contractTemplates, {
    fields: [purchaseContracts.template_id],
    references: [contractTemplates.id],
  }),
  items: many(purchaseContractItems),
}))

export const purchaseContractItemsRelations = relations(purchaseContractItems, ({ one }) => ({
  contract: one(purchaseContracts, {
    fields: [purchaseContractItems.contract_id],
    references: [purchaseContracts.id],
  }),
  product: one(products, {
    fields: [purchaseContractItems.product_id],
    references: [products.id],
  }),
}))

export const contractTemplatesRelations = relations(contractTemplates, ({ one, many }) => ({
  company: one(companies, {
    fields: [contractTemplates.company_id],
    references: [companies.id],
  }),
  salesContracts: many(salesContracts),
  purchaseContracts: many(purchaseContracts),
}))
