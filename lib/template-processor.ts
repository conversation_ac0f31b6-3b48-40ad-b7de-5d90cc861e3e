import { db } from "@/lib/db"
import { customers, suppliers, products } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"

// Template variable types
export interface TemplateVariables {
  // Contract details
  contract_number?: string
  contract_date?: string
  contract_status?: string
  
  // Customer/Supplier details
  customer_name?: string
  customer_email?: string
  customer_address?: string
  supplier_name?: string
  supplier_email?: string
  supplier_address?: string
  
  // Product details
  product_list?: string
  total_amount?: string
  currency?: string
  
  // Terms
  payment_terms?: string
  delivery_terms?: string
  
  // System details
  current_date?: string
  current_year?: string
}

// Contract item interface for product list generation
export interface ContractItem {
  product_id: string
  qty: string
  price: string
  product?: {
    name: string
    description?: string
  }
}

/**
 * Process template content by replacing variables with actual data
 */
export async function processTemplateContent(
  templateContent: string,
  variables: TemplateVariables
): Promise<string> {
  let processedContent = templateContent

  // Replace all variables in the template
  for (const [key, value] of Object.entries(variables)) {
    if (value !== undefined && value !== null) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
      processedContent = processedContent.replace(regex, String(value))
    }
  }

  // Remove any unreplaced variables (optional - could also leave them as is)
  processedContent = processedContent.replace(/{{[^}]+}}/g, '[Variable not available]')

  return processedContent
}

/**
 * Generate variables for sales contract
 */
export async function generateSalesContractVariables(contractData: {
  number: string
  customer_id: string
  date: string
  status: string
  currency?: string
  payment_terms?: string
  delivery_terms?: string
  items: ContractItem[]
}): Promise<TemplateVariables> {
  // Get customer details
  const customer = await db.query.customers.findFirst({
    where: eq(customers.id, contractData.customer_id),
  })

  // Generate product list
  const productList = await generateProductList(contractData.items)
  const totalAmount = calculateTotalAmount(contractData.items, contractData.currency)

  return {
    contract_number: contractData.number,
    contract_date: contractData.date,
    contract_status: contractData.status,
    customer_name: customer?.name || '[Customer Name]',
    customer_email: customer?.email || '[Customer Email]',
    customer_address: customer?.address || '[Customer Address]',
    product_list: productList,
    total_amount: totalAmount,
    currency: contractData.currency || 'USD',
    payment_terms: contractData.payment_terms || '[Payment Terms]',
    delivery_terms: contractData.delivery_terms || '[Delivery Terms]',
    current_date: new Date().toLocaleDateString(),
    current_year: new Date().getFullYear().toString(),
  }
}

/**
 * Generate variables for purchase contract
 */
export async function generatePurchaseContractVariables(contractData: {
  number: string
  supplier_id: string
  date: string
  status: string
  currency?: string
  payment_terms?: string
  delivery_terms?: string
  items: ContractItem[]
}): Promise<TemplateVariables> {
  // Get supplier details
  const supplier = await db.query.suppliers.findFirst({
    where: eq(suppliers.id, contractData.supplier_id),
  })

  // Generate product list
  const productList = await generateProductList(contractData.items)
  const totalAmount = calculateTotalAmount(contractData.items, contractData.currency)

  return {
    contract_number: contractData.number,
    contract_date: contractData.date,
    contract_status: contractData.status,
    supplier_name: supplier?.name || '[Supplier Name]',
    supplier_email: supplier?.email || '[Supplier Email]',
    supplier_address: supplier?.address || '[Supplier Address]',
    product_list: productList,
    total_amount: totalAmount,
    currency: contractData.currency || 'USD',
    payment_terms: contractData.payment_terms || '[Payment Terms]',
    delivery_terms: contractData.delivery_terms || '[Delivery Terms]',
    current_date: new Date().toLocaleDateString(),
    current_year: new Date().getFullYear().toString(),
  }
}

/**
 * Generate formatted product list from contract items
 */
async function generateProductList(items: ContractItem[]): Promise<string> {
  const productLines = await Promise.all(
    items.map(async (item) => {
      let productName = '[Product Name]'
      
      if (item.product?.name) {
        productName = item.product.name
      } else if (item.product_id) {
        // Fetch product details if not provided
        const product = await db.query.products.findFirst({
          where: eq(products.id, item.product_id),
        })
        productName = product?.name || '[Product Name]'
      }

      const qty = parseFloat(item.qty) || 0
      const price = parseFloat(item.price) || 0
      const lineTotal = qty * price

      return `${productName} - Qty: ${qty}, Price: $${price.toFixed(2)}, Total: $${lineTotal.toFixed(2)}`
    })
  )

  return productLines.join('\n')
}

/**
 * Calculate total amount from contract items
 */
function calculateTotalAmount(items: ContractItem[], currency = 'USD'): string {
  const total = items.reduce((sum, item) => {
    const qty = parseFloat(item.qty) || 0
    const price = parseFloat(item.price) || 0
    return sum + (qty * price)
  }, 0)

  return `${currency} ${total.toFixed(2)}`
}

/**
 * Sanitize template content to prevent XSS
 */
export function sanitizeTemplateContent(content: string): string {
  // Basic HTML sanitization - remove script tags and dangerous attributes
  return content
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
}

/**
 * Validate template variables syntax
 */
export function validateTemplateVariables(content: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  const variableRegex = /{{([^}]+)}}/g
  const matches = content.match(variableRegex)

  if (matches) {
    for (const match of matches) {
      const variableName = match.replace(/[{}]/g, '').trim()
      
      // Check for valid variable names (alphanumeric and underscore only)
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(variableName)) {
        errors.push(`Invalid variable name: ${variableName}`)
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Get list of available template variables
 */
export function getAvailableVariables(): { [key: string]: string } {
  return {
    contract_number: 'Contract number (e.g., SC-2025-001)',
    contract_date: 'Contract date',
    contract_status: 'Contract status',
    customer_name: 'Customer name (sales contracts)',
    customer_email: 'Customer email (sales contracts)',
    customer_address: 'Customer address (sales contracts)',
    supplier_name: 'Supplier name (purchase contracts)',
    supplier_email: 'Supplier email (purchase contracts)',
    supplier_address: 'Supplier address (purchase contracts)',
    product_list: 'Formatted list of products with quantities and prices',
    total_amount: 'Total contract amount with currency',
    currency: 'Contract currency',
    payment_terms: 'Payment terms',
    delivery_terms: 'Delivery terms',
    current_date: 'Current date',
    current_year: 'Current year',
  }
}
