import { z } from "zod"

// Base validation schemas for core entities
export const contactSchema = z.object({
  name: z.string().min(1, "Contact name is required").max(255),
  phone: z.string().max(50).optional(),
  email: z.string().email("Invalid email format").optional().or(z.literal(""))
})

export const customerSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Customer name is required").max(255),
  contact_name: z.string().max(255).optional(),
  contact_phone: z.string().max(50).optional(),
  contact_email: z.string().email("Invalid email format").optional().or(z.literal("")),
  address: z.string().max(500).optional(),
  tax_id: z.string().max(50).optional(),
  bank: z.string().max(255).optional(),
  incoterm: z.string().max(50).optional(),
  payment_term: z.string().max(100).optional(),
  status: z.enum(["active", "inactive"]).default("active")
})

export const supplierSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Supplier name is required").max(255),
  contact: contactSchema.optional(),
  address: z.string().max(500).optional(),
  taxId: z.string().max(50).optional(),
  bank: z.string().max(255).optional(),
  status: z.enum(["active", "inactive"]).default("active")
})

export const productSchema = z.object({
  id: z.string().optional(),
  sku: z.string().min(1, "SKU is required").max(100),
  name: z.string().min(1, "Product name is required").max(255),
  unit: z.string().min(1, "Unit is required").max(50),
  hsCode: z.string().max(20).optional(),
  origin: z.string().max(100).optional(),
  package: z.string().max(255).optional(),
  image: z.string().url("Invalid image URL").optional().or(z.literal(""))
})

export const sampleSchema = z.object({
  id: z.string().optional(),
  code: z.string().min(1, "Sample code is required").max(100),
  name: z.string().min(1, "Sample name is required").max(255),
  image: z.string().url("Invalid image URL").optional().or(z.literal("")),
  specification: z.string().max(1000).optional(),
  date: z.string().datetime().optional(),
  moq: z.number().int().min(1, "MOQ must be at least 1").optional(),
  availableFromStock: z.boolean().default(false),
  adminNotes: z.string().max(1000).optional()
})

// Contract validation schemas
export const contractItemSchema = z.object({
  product_id: z.string().min(1, "Product is required").optional(),
  productId: z.string().min(1, "Product is required").optional(), // Support both for compatibility
  qty: z.coerce.number().positive("Quantity must be positive"), // Coerce string to number
  price: z.coerce.number().positive("Price must be positive") // Coerce string to number
}).refine(data => data.product_id || data.productId, {
  message: "Product is required",
  path: ["productId"]
})

export const salesContractSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Contract number is required").max(100),
  customer_id: z.string().min(1, "Customer is required"),
  customerId: z.string().min(1, "Customer is required").optional(), // Support both for compatibility
  date: z.string().optional(),
  template_id: z.string().optional(),
  templateId: z.string().optional(), // Support both field names
  status: z.enum(["draft", "review", "approved", "active"]).default("draft"),
  currency: z.string().min(1, "Currency is required").max(10).optional(),
  items: z.array(contractItemSchema).min(1, "At least one item is required")
})

export const purchaseContractSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Contract number is required").max(100),
  supplierId: z.string().min(1, "Supplier is required"),
  supplier_id: z.string().min(1, "Supplier is required").optional(), // Support both field names
  date: z.string().optional(), // Accept date string format
  template_id: z.string().optional(),
  templateId: z.string().optional(), // Support both field names
  status: z.enum(["draft", "review", "approved", "active"]).default("draft"),
  currency: z.string().min(1, "Currency is required").max(10).optional(), // Make optional
  items: z.array(contractItemSchema).min(1, "At least one item is required")
})

// Contract Template Validation Schema
export const contractTemplateSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Template name is required").max(200),
  type: z.enum(["sales", "purchase"], {
    required_error: "Template type is required",
    invalid_type_error: "Template type must be either 'sales' or 'purchase'"
  }),
  content: z.string().min(1, "Template content is required"),
  currency: z.string().max(10).optional(),
  payment_terms: z.string().max(500).optional(),
  delivery_terms: z.string().max(500).optional(),
  language: z.string().max(10).default("en"),
  version: z.number().int().positive().default(1),
  is_active: z.boolean().default(true)
})

// Production validation schemas
export const workOperationSchema = z.object({
  name: z.string().min(1, "Operation name is required").max(255),
  status: z.enum(["not-started", "in-progress", "done"]).default("not-started"),
  startedAt: z.string().datetime().optional(),
  completedAt: z.string().datetime().optional()
})

export const workOrderSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Work order number is required").max(100),
  salesContractId: z.string().min(1, "Sales contract is required"),
  productId: z.string().min(1, "Product is required"),
  qty: z.number().int().positive("Quantity must be positive"),
  operations: z.array(workOperationSchema).optional()
})

// Inventory validation schemas
export const stockLotSchema = z.object({
  id: z.string().optional(),
  productId: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  note: z.string().max(500).optional(),
  createdAt: z.string().datetime().optional()
})

export const stockTxnSchema = z.object({
  id: z.string().optional(),
  type: z.enum(["inbound", "outbound"]),
  productId: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  ref: z.string().max(255).optional(),
  createdAt: z.string().datetime().optional()
})

// Export validation schemas
export const declarationItemSchema = z.object({
  productId: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  hsCode: z.string().max(20).optional()
})

export const declarationSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Declaration number is required").max(100),
  date: z.string().datetime().optional(),
  status: z.enum(["draft", "submitted", "cleared", "exception"]).default("draft"),
  items: z.array(declarationItemSchema).min(1, "At least one item is required"),
  packingPhotoUrls: z.array(z.string().url()).optional(),
  taxRefundInfo: z.string().max(1000).optional()
})

// Finance validation schemas
export const arInvoiceSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Invoice number is required").max(100),
  customerId: z.string().min(1, "Customer is required"),
  date: z.string().datetime().optional(),
  currency: z.string().min(1, "Currency is required").max(10),
  amount: z.number().positive("Amount must be positive"),
  received: z.number().min(0, "Received amount cannot be negative").default(0)
})

export const apInvoiceSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Invoice number is required").max(100),
  supplierId: z.string().min(1, "Supplier is required"),
  date: z.string().datetime().optional(),
  currency: z.string().min(1, "Currency is required").max(10),
  amount: z.number().positive("Amount must be positive"),
  paid: z.number().min(0, "Paid amount cannot be negative").default(0)
})

// Inbound/Outbound operation schemas
export const inboundSchema = z.object({
  product_id: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  note: z.string().max(500).optional()
})

export const outboundSchema = z.object({
  product_id: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  ref: z.string().max(255).optional()
})

// Quality Control validation schemas
export const qualityInspectionSchema = z.object({
  id: z.string().optional(),
  work_order_id: z.string().min(1, "Work order is required"),
  inspection_type: z.enum(["incoming", "in-process", "final", "pre-shipment"], {
    errorMap: () => ({ message: "Invalid inspection type" })
  }),
  inspector: z.string().min(1, "Inspector name is required").max(255),
  scheduled_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  completed_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  status: z.enum(["scheduled", "in-progress", "completed", "failed"]).default("scheduled"),
  notes: z.string().max(1000).optional()
})

export const qualityDefectSchema = z.object({
  id: z.string().optional(),
  inspection_id: z.string().min(1, "Inspection is required").optional(),
  work_order_id: z.string().min(1, "Work order is required"),
  product_id: z.string().min(1, "Product is required"),
  defect_type: z.enum(["visual", "dimensional", "functional", "material"], {
    errorMap: () => ({ message: "Invalid defect type" })
  }),
  severity: z.enum(["minor", "major", "critical"], {
    errorMap: () => ({ message: "Invalid severity level" })
  }),
  quantity: z.string().min(1, "Quantity is required"),
  description: z.string().min(1, "Description is required").max(1000),
  corrective_action: z.string().max(1000).optional(),
  status: z.enum(["open", "in-progress", "resolved", "closed"]).default("open")
})

export const qualityStandardSchema = z.object({
  id: z.string().optional(),
  product_id: z.string().min(1, "Product is required"),
  standard_name: z.string().min(1, "Standard name is required").max(255),
  specification: z.string().min(1, "Specification is required").max(1000),
  tolerance: z.string().max(255).optional(),
  test_method: z.string().max(500).optional(),
  acceptance_criteria: z.string().min(1, "Acceptance criteria is required").max(1000)
})

export const qualityCertificateSchema = z.object({
  id: z.string().optional(),
  inspection_id: z.string().min(1, "Inspection is required"),
  certificate_number: z.string().min(1, "Certificate number is required").max(100),
  certificate_type: z.enum(["COA", "COC", "test_report", "compliance"], {
    errorMap: () => ({ message: "Invalid certificate type" })
  }),
  issued_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  valid_until: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  file_path: z.string().max(500).optional()
})

export const inspectionResultSchema = z.object({
  id: z.string().optional(),
  inspection_id: z.string().min(1, "Inspection is required"),
  standard_id: z.string().min(1, "Standard is required").optional(),
  measured_value: z.string().max(255).optional(),
  result: z.enum(["pass", "fail", "conditional"], {
    errorMap: () => ({ message: "Invalid result value" })
  }),
  notes: z.string().max(1000).optional()
})
