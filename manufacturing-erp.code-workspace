{"folders": [{"name": "Manufacturing ERP", "path": "."}], "settings": {"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.css": "tailwindcss", "*.tsx": "typescriptreact", "*.ts": "typescript"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "files.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true, "**/.git": true}, "search.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true}}, "extensions": {"recommendations": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "ms-vscode.vscode-css-peek", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-eslint", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "ms-vscode.vscode-css-peek", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "ms-vscode.vscode-css-peek", "sentry.sentry-vscode", "ms-vscode.vscode-postgres"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "dev", "type": "shell", "command": "npm run dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "build", "type": "shell", "command": "npm run build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "test-health", "type": "shell", "command": "curl http://localhost:3000/api/health", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "install", "type": "shell", "command": "npm install", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}, "launch": {"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev"}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "npm run dev", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}]}}