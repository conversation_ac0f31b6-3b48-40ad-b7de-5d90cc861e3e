{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx lib/database/seed.ts", "db:reset": "npm run db:migrate", "migrate:multi-tenant": "tsx scripts/migrate-multi-tenant.ts", "test:security": "tsx scripts/test-multi-tenant-security.ts", "test:comprehensive-security": "tsx scripts/comprehensive-security-test.ts", "diagnose:isolation": "tsx scripts/diagnose-tenant-isolation.ts", "setup:test-user": "tsx scripts/create-test-user-session.ts", "auth0:manage-users": "tsx scripts/auth0-user-management.ts", "auth0:verify-setup": "tsx scripts/verify-auth0-setup.ts", "auth0:create-user": "tsx scripts/create-auth0-user-programmatically.ts", "test:auto-company": "tsx scripts/test-auto-company-creation.ts", "create:test-user": "tsx scripts/create-test-user-company.ts", "auth0:create-guide": "tsx scripts/create-auth0-test-user.ts", "setup:security": "tsx scripts/complete-security-setup.ts", "test": "jest", "test:unit": "jest --testPathPattern=__tests__/lib", "test:integration": "jest --testPathPattern=__tests__/api", "test:e2e": "playwright test", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "prepare": "husky install", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist coverage test-results", "postinstall": "npm run db:generate"}, "dependencies": {"@auth0/nextjs-auth0": "^3.8.0", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@react-pdf/renderer": "^4.3.0", "@types/html2canvas": "^0.5.35", "@vercel/blob": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "drizzle-orm": "^0.36.4", "embla-carousel-react": "8.5.1", "geist": "^1.3.1", "glob": "^11.0.3", "html2canvas": "^1.4.1", "input-otp": "1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "postgres": "^3.4.7", "react": "^19", "react-day-picker": "9.8.0", "react-dom": "^19", "react-hook-form": "^7.60.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "3.25.67"}, "devDependencies": {"@playwright/test": "^1.42.1", "@tailwindcss/postcss": "^4.1.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/d3-array": "^3.2.1", "@types/d3-color": "^3.1.3", "@types/d3-ease": "^3.0.2", "@types/d3-interpolate": "^3.0.4", "@types/d3-path": "^3.1.0", "@types/d3-scale": "^4.0.8", "@types/d3-shape": "^3.1.6", "@types/d3-time": "^3.0.3", "@types/d3-timer": "^3.0.2", "@types/jest": "^29.5.12", "@types/node": "^22", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "drizzle-kit": "^0.30.0", "eslint": "^8.57.0", "eslint-config-next": "^14.1.0", "eslint-config-prettier": "^9.1.0", "husky": "^9.0.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5", "prettier": "^3.2.5", "tailwindcss": "^4.1.9", "tsx": "^4.7.1", "tw-animate-css": "1.3.3", "typescript": "^5"}}