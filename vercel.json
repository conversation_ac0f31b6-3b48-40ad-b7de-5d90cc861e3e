{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/next"}], "env": {"DATABASE_URL": "*****************************************************************/postgres", "DATABASE_URL_POSTGRESQL": "*****************************************************************/postgres", "USE_POSTGRESQL": "true", "MIGRATION_PHASE": "production", "ENFORCE_TENANT_ISOLATION": "true", "ENABLE_AUDIT_LOGGING": "true", "AUTH0_SECRET": "****************************************************************", "AUTH0_BASE_URL": "https://silk-road-john.vercel.app", "AUTH0_ISSUER_BASE_URL": "https://dev-tejx02ztaj7oufoc.us.auth0.com", "AUTH0_CLIENT_ID": "To2oAQX05DtstsgKXdLvsUYAaRO23QCK", "AUTH0_CLIENT_SECRET": "****************************************************************", "SENTRY_ORG": "dassodev", "SENTRY_PROJECT": "manufacturing-erp", "SENTRY_DSN": "https://<EMAIL>/4509869772374016", "NEXT_PUBLIC_SENTRY_DSN": "https://<EMAIL>/4509869772374016"}}